.audio-wave {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 2px;
  position: relative;
}

.audio-bar {
  width: calc((100% - (var(--bar-count) - 1) * 2px) / var(--bar-count));
  min-height: 2px;
  background: linear-gradient(180deg, 
    rgba(10, 132, 255, 0.8) 0%, 
    rgba(0, 122, 255, 0.6) 100%);
  border-radius: 2px;
  transition: all 0.3s ease;
  transform-origin: bottom;
}

/* Inactive state - subtle pulse */
.audio-wave.inactive .audio-bar {
  height: 20%;
  opacity: 0.3;
  animation: subtle-pulse 2s ease-in-out infinite;
  animation-delay: var(--delay);
}

/* Active state - dynamic wave animation */
.audio-wave.active .audio-bar {
  height: 100%;
  opacity: 1;
  animation: wave-dance 0.8s ease-in-out infinite alternate;
  animation-delay: var(--delay);
  box-shadow: 0 0 8px rgba(10, 132, 255, 0.4);
}

/* Subtle pulse for inactive state */
@keyframes subtle-pulse {
  0%, 100% { 
    transform: scaleY(0.8);
    opacity: 0.3;
  }
  50% { 
    transform: scaleY(1.2);
    opacity: 0.5;
  }
}

/* Dynamic wave animation for active state */
@keyframes wave-dance {
  0% { 
    transform: scaleY(0.3);
    filter: brightness(0.8);
  }
  25% { 
    transform: scaleY(0.8);
    filter: brightness(1.1);
  }
  50% { 
    transform: scaleY(1.2);
    filter: brightness(1.3);
  }
  75% { 
    transform: scaleY(0.9);
    filter: brightness(1.1);
  }
  100% { 
    transform: scaleY(0.6);
    filter: brightness(0.9);
  }
}

/* Alternative style: Dots */
.audio-wave.dots .audio-bar {
  border-radius: 50%;
  width: 8px;
  height: 8px;
  min-height: 8px;
}

.audio-wave.dots.active .audio-bar {
  animation: dot-bounce 0.6s ease-in-out infinite alternate;
}

@keyframes dot-bounce {
  0% { 
    transform: scale(0.8);
    opacity: 0.6;
  }
  100% { 
    transform: scale(1.4);
    opacity: 1;
  }
}

/* Alternative style: Gradient wave */
.audio-wave.gradient {
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(10, 132, 255, 0.1) 20%, 
    rgba(10, 132, 255, 0.3) 50%, 
    rgba(10, 132, 255, 0.1) 80%, 
    transparent 100%);
  border-radius: 12px;
  overflow: hidden;
}

.audio-wave.gradient .audio-bar {
  background: rgba(10, 132, 255, 0.8);
  border-radius: 1px;
}

.audio-wave.gradient.active {
  animation: gradient-flow 1.5s ease-in-out infinite;
}

@keyframes gradient-flow {
  0%, 100% {
    background-position: -100% 0;
  }
  50% {
    background-position: 100% 0;
  }
}
