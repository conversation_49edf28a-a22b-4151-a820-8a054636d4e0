// Simple 3-dot audio visualizer
$dot-color: #0a84ff;
$dot-size: 8px;
$dot-gap: 4px;

.audio-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $dot-gap;

  .dot {
    width: $dot-size;
    height: $dot-size;
    background: $dot-color;
    border-radius: 50%;
    opacity: 0.3;
    transition: all 0.2s ease;
  }

  // When mic is active but not speaking - subtle pulse
  &.active .dot {
    opacity: 0.6;
    animation: subtle-pulse 2s ease-in-out infinite;
    animation-delay: var(--delay);
  }

  // When actually speaking - react to audio level
  &.speaking .dot {
    opacity: 1;
    animation: audio-react 0.3s ease-in-out infinite;
    animation-delay: var(--delay);
    transform: scale(calc(1 + var(--audio-level) * 0.5));
    box-shadow: 0 0 calc(var(--audio-level) * 10px) rgba(10, 132, 255, 0.6);
  }
}

@keyframes subtle-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes audio-react {
  0% {
    transform: scale(calc(1 + var(--audio-level) * 0.3));
  }
  50% {
    transform: scale(calc(1 + var(--audio-level) * 0.7));
  }
  100% {
    transform: scale(calc(1 + var(--audio-level) * 0.3));
  }
}
