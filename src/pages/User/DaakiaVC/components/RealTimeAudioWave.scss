// SCSS Variables
$primary-blue: rgba(10, 132, 255, 0.8);
$secondary-blue: rgba(0, 122, 255, 0.6);
$light-blue: rgba(10, 132, 255, 0.4);
$very-light-blue: rgba(10, 132, 255, 0.1);

$bar-gap: 2px;
$border-radius: 2px;
$transition-duration: 0.3s;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

@mixin bar-gradient($opacity1: 0.8, $opacity2: 0.6) {
  background: linear-gradient(180deg, 
    rgba(10, 132, 255, $opacity1) 0%, 
    rgba(0, 122, 255, $opacity2) 100%);
}

// Main component
.audio-wave {
  @include flex-center;
  gap: $bar-gap;
  position: relative;

  .audio-bar {
    width: calc((100% - (var(--bar-count) - 1) * #{$bar-gap}) / var(--bar-count));
    min-height: 2px;
    @include bar-gradient;
    border-radius: $border-radius;
    transition: all $transition-duration ease;
    transform-origin: bottom;
  }

  // Inactive state - subtle pulse
  &.inactive .audio-bar {
    height: 20%;
    opacity: 0.3;
    animation: subtle-pulse 2s ease-in-out infinite;
    animation-delay: var(--delay);
  }

  // Active state - dynamic wave animation
  &.active .audio-bar {
    height: 100%;
    opacity: 1;
    animation: wave-dance 0.8s ease-in-out infinite alternate;
    animation-delay: var(--delay);
    box-shadow: 0 0 8px $light-blue;
  }

  // Alternative style: Dots
  &.dots {
    .audio-bar {
      border-radius: 50%;
      width: 8px;
      height: 8px;
      min-height: 8px;
    }

    &.active .audio-bar {
      animation: dot-bounce 0.6s ease-in-out infinite alternate;
    }
  }

  // Alternative style: Gradient wave
  &.gradient {
    background: linear-gradient(90deg, 
      transparent 0%, 
      $very-light-blue 20%, 
      rgba(10, 132, 255, 0.3) 50%, 
      $very-light-blue 80%, 
      transparent 100%);
    border-radius: 12px;
    overflow: hidden;

    .audio-bar {
      background: $primary-blue;
      border-radius: 1px;
    }

    &.active {
      animation: gradient-flow 1.5s ease-in-out infinite;
    }
  }
}

// Keyframe animations
@keyframes subtle-pulse {
  0%, 100% { 
    transform: scaleY(0.8);
    opacity: 0.3;
  }
  50% { 
    transform: scaleY(1.2);
    opacity: 0.5;
  }
}

@keyframes wave-dance {
  0% { 
    transform: scaleY(0.3);
    filter: brightness(0.8);
  }
  25% { 
    transform: scaleY(0.8);
    filter: brightness(1.1);
  }
  50% { 
    transform: scaleY(1.2);
    filter: brightness(1.3);
  }
  75% { 
    transform: scaleY(0.9);
    filter: brightness(1.1);
  }
  100% { 
    transform: scaleY(0.6);
    filter: brightness(0.9);
  }
}

@keyframes dot-bounce {
  0% { 
    transform: scale(0.8);
    opacity: 0.6;
  }
  100% { 
    transform: scale(1.4);
    opacity: 1;
  }
}

@keyframes gradient-flow {
  0%, 100% {
    background-position: -100% 0;
  }
  50% {
    background-position: 100% 0;
  }
}
