import React, { useEffect, useRef, useState } from 'react';
import { useLocalParticipant } from '@livekit/components-react';
import './RealTimeAudioWave.scss';

function RealTimeAudioWave({
  className = '',
  width = 60,
  height = 24,
}) {
  const { localParticipant } = useLocalParticipant();
  const [audioLevel, setAudioLevel] = useState(0);
  const analyserRef = useRef();
  const animationRef = useRef();

  useEffect(() => {
    let audioContext;
    let sourceNode;

    const updateAudioLevel = () => {
      if (!analyserRef.current) return;

      const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
      analyserRef.current.getByteFrequencyData(dataArray);

      // Calculate average volume
      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      const normalizedLevel = Math.min(average / 128, 1);

      setAudioLevel(normalizedLevel);
      animationRef.current = requestAnimationFrame(updateAudioLevel);
    };

    const setupAudioAnalysis = async () => {
      try {
        if (!localParticipant?.isMicrophoneEnabled) {
          setAudioLevel(0);
          return;
        }

        const micTrack = localParticipant.getTrackPublication('microphone')?.track;
        if (!micTrack?.mediaStream) return;

        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyserRef.current = audioContext.createAnalyser();
        analyserRef.current.fftSize = 256;
        analyserRef.current.smoothingTimeConstant = 0.8;

        sourceNode = audioContext.createMediaStreamSource(micTrack.mediaStream);
        sourceNode.connect(analyserRef.current);

        updateAudioLevel();
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        setAudioLevel(0);
      }
    };

    setupAudioAnalysis();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      if (sourceNode) {
        sourceNode.disconnect();
      }
    };
  }, [localParticipant?.isMicrophoneEnabled]);

  const isActive = localParticipant?.isMicrophoneEnabled;
  const isSpeaking = audioLevel > 0.1; // Threshold for speaking

  return (
    <div
      className={`audio-dots ${className} ${isActive ? 'active' : 'inactive'} ${isSpeaking ? 'speaking' : ''}`}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        '--audio-level': audioLevel
      }}
    >
      <div className="dot" style={{ '--delay': '0s' }} />
      <div className="dot" style={{ '--delay': '0.1s' }} />
      <div className="dot" style={{ '--delay': '0.2s' }} />
    </div>
  );
}

export default RealTimeAudioWave;
