import React from 'react';
import { useLocalParticipant } from '@livekit/components-react';
import './RealTimeAudioWave.css';

function RealTimeAudioWave({
  className = '',
  width = 60,
  height = 24,
  barCount = 5,
}) {
  const { localParticipant } = useLocalParticipant();
  const isActive = localParticipant?.isMicrophoneEnabled;

  return (
    <div
      className={`audio-wave ${className} ${isActive ? 'active' : 'inactive'}`}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        '--bar-count': barCount
      }}
    >
      {Array.from({ length: barCount }, (_, index) => (
        <div
          key={index}
          className="audio-bar"
          style={{ '--delay': `${index * 0.1}s` }}
        />
      ))}
    </div>
  );
}

export default RealTimeAudioWave;
