import React, { useEffect, useRef, useState } from 'react';
import { useLocalParticipant } from '@livekit/components-react';

function RealTimeAudioWave({
  className = '',
  width = 60,
  height = 24,
  sensitivity = 2.0,
  barCount = 7,
}) {
  const [audioLevels, setAudioLevels] = useState(new Array(barCount).fill(0));
  const [isActive, setIsActive] = useState(false);
  const animationRef = useRef();
  const analyserRef = useRef();
  const dataArrayRef = useRef();
  const { localParticipant } = useLocalParticipant();

  useEffect(() => {
    let audioContext;
    let sourceNode;

    const updateAudioData = () => {
      if (!analyserRef.current || !dataArrayRef.current) return;

      analyserRef.current.getByteFrequencyData(dataArrayRef.current);
      
      // Calculate average volume for overall level
      const average = dataArrayRef.current.reduce((sum, value) => sum + value, 0) / dataArrayRef.current.length;
      const normalizedLevel = Math.min(average / 128 * sensitivity, 1);
      
      // Create individual bar levels with wave-like variations
      const newLevels = new Array(barCount).fill(0).map((_, index) => {
        const freqIndex = Math.floor((index / barCount) * dataArrayRef.current.length);
        const freqLevel = dataArrayRef.current[freqIndex] / 255;
        const baseLevel = normalizedLevel * 0.7;
        
        // Add wave-like variation based on position
        const waveVariation = Math.sin((index / barCount) * Math.PI * 2 + Date.now() * 0.01) * 0.15;
        const randomVariation = (Math.random() - 0.5) * 0.1;
        
        return Math.max(0, Math.min(1, baseLevel + freqLevel * 0.3 + waveVariation + randomVariation));
      });
      setAudioLevels(newLevels);

      animationRef.current = requestAnimationFrame(updateAudioData);
    };

    const setupAudioAnalysis = async () => {
      try {
        if (!localParticipant) return;

        const micTrack = localParticipant.getTrackPublication('microphone')?.track;
        if (!micTrack || !micTrack.mediaStream) return;

        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyserRef.current = audioContext.createAnalyser();
        analyserRef.current.fftSize = 256;
        analyserRef.current.smoothingTimeConstant = 0.8;

        const bufferLength = analyserRef.current.frequencyBinCount;
        dataArrayRef.current = new Uint8Array(bufferLength);

        sourceNode = audioContext.createMediaStreamSource(micTrack.mediaStream);
        sourceNode.connect(analyserRef.current);

        setIsActive(true);
        updateAudioData();
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        setIsActive(false);
      }
    };

    if (localParticipant?.isMicrophoneEnabled) {
      setupAudioAnalysis();
    } else {
      setIsActive(false);
      setAudioLevels(new Array(barCount).fill(0));
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      if (sourceNode) {
        sourceNode.disconnect();
      }
    };
  }, [localParticipant?.isMicrophoneEnabled, sensitivity, barCount]);

  const renderWaveBars = () => {
    const barWidth = Math.floor(width / barCount) - 2;
    const maxBarHeight = height;

    return (
      <>
        <style>
          {`
            @keyframes waveMove {
              0% { transform: translateX(-100%); }
              100% { transform: translateX(100%); }
            }
          `}
        </style>
        <div
          style={{
            width: `${width}px`,
            height: `${height}px`,
            display: 'flex',
            alignItems: 'flex-end',
            justifyContent: 'center',
            gap: '2px',
            position: 'relative'
          }}
        >
          {audioLevels.map((level, index) => {
            const barHeight = Math.max(2, level * maxBarHeight);
            const intensity = isActive && level > 0.05 ? level : 0.3;
            const glowIntensity = isActive && level > 0.1 ? level * 0.8 : 0;
            
            // Create gradient based on level
            const gradientStart = `rgba(10, 132, 255, ${intensity})`;
            const gradientEnd = `rgba(0, 122, 255, ${intensity * 0.6})`;
            
            return (
              <div
                key={index}
                style={{
                  width: `${barWidth}px`,
                  height: `${barHeight}px`,
                  background: `linear-gradient(180deg, ${gradientStart} 0%, ${gradientEnd} 100%)`,
                  borderRadius: '2px',
                  transition: 'height 0.1s ease-out, background 0.2s ease',
                  position: 'relative',
                  boxShadow: glowIntensity > 0 
                    ? `0 0 ${glowIntensity * 8}px rgba(10, 132, 255, ${glowIntensity * 0.6})`
                    : 'none',
                  transform: `scaleY(${isActive ? 1 : 0.8})`,
                  opacity: isActive ? 1 : 0.4
                }}
              >
                {/* Inner highlight */}
                <div
                  style={{
                    position: 'absolute',
                    top: '0',
                    left: '0',
                    right: '0',
                    height: '30%',
                    background: `linear-gradient(180deg, rgba(255,255,255,${intensity * 0.3}) 0%, transparent 100%)`,
                    borderRadius: '2px 2px 0 0',
                    opacity: isActive ? 1 : 0.3
                  }}
                />
                
                {/* Wave effect overlay */}
                {isActive && level > 0.2 && (
                  <div
                    style={{
                      position: 'absolute',
                      top: '0',
                      left: '0',
                      right: '0',
                      bottom: '0',
                      background: `linear-gradient(90deg, 
                        transparent 0%, 
                        rgba(255,255,255,${level * 0.2}) 20%, 
                        rgba(255,255,255,${level * 0.4}) 50%, 
                        rgba(255,255,255,${level * 0.2}) 80%, 
                        transparent 100%)`,
                      borderRadius: '2px',
                      animation: 'waveMove 1.5s ease-in-out infinite',
                      opacity: level * 0.6
                    }}
                  />
                )}
              </div>
            );
          })}
        </div>
      </>
    );
  };

  return (
    <div className={`real-time-audio-wave ${className}`}>
      {renderWaveBars()}
    </div>
  );
}

export default RealTimeAudioWave;
